/**
 * Galaxy API Client - <PERSON><PERSON> thế Firebase
 */

class GalaxyAPI {
  constructor() {
    this.baseUrl = this.getBaseUrl();
  }

  /**
   * Lấy base URL của API
   */
  getBaseUrl() {
    const protocol = window.location.protocol;
    const host = window.location.host;
    const path = window.location.pathname.substring(
      0,
      window.location.pathname.lastIndexOf("/")
    );
    return `${protocol}//${host}${path}`;
  }

  /**
   * Tạo galaxy mới
   */
  async createGalaxy(data) {
    try {
      const response = await fetch(`${this.baseUrl}/api/create-galaxy.php`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || "Failed to create galaxy");
      }

      return result;
    } catch (error) {
      console.error("Error creating galaxy:", error);
      throw error;
    }
  }

  /**
   * <PERSON><PERSON><PERSON> thông tin galaxy
   */
  async getGalaxy(galaxyId) {
    try {
      const response = await fetch(
        `${this.baseUrl}/api/get-galaxy.php?id=${encodeURIComponent(galaxyId)}`
      );
      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || "Failed to get galaxy");
      }

      return result.data;
    } catch (error) {
      console.error("Error getting galaxy:", error);
      throw error;
    }
  }

  /**
   * Upload ảnh
   */
  async uploadImages(files) {
    try {
      const formData = new FormData();

      // Thêm files vào FormData
      if (files.length > 0) {
        for (let i = 0; i < files.length; i++) {
          formData.append("images[]", files[i]);
        }
      }

      const response = await fetch(`${this.baseUrl}/api/upload-image.php`, {
        method: "POST",
        body: formData,
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || "Failed to upload images");
      }

      return result.urls;
    } catch (error) {
      console.error("Error uploading images:", error);
      throw error;
    }
  }

  /**
   * Upload bài hát
   */
  async uploadSong(file) {
    try {
      const formData = new FormData();
      formData.append("song", file);

      const response = await fetch(`${this.baseUrl}/api/upload-song.php`, {
        method: "POST",
        body: formData,
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || "Failed to upload song");
      }

      return result.filename;
    } catch (error) {
      console.error("Error uploading song:", error);
      throw error;
    }
  }

  /**
   * Validate dữ liệu trước khi gửi
   */
  validateGalaxyData(data) {
    const errors = [];

    // Validate messages
    if (
      !data.messages ||
      !Array.isArray(data.messages) ||
      data.messages.length === 0
    ) {
      errors.push("Tin nhắn không được để trống");
    }

    // Validate colors
    if (data.colors) {
      const colorKeys = ["love", "birthday", "date", "special", "heart"];
      for (const key of colorKeys) {
        if (data.colors[key] && !this.isValidHexColor(data.colors[key])) {
          errors.push(`Màu ${key} không hợp lệ`);
        }
      }
    }

    // Validate icons
    if (data.icons && Array.isArray(data.icons)) {
      if (data.icons.length > 20) {
        errors.push("Tối đa 20 biểu tượng");
      }
    }

    // Validate images
    if (data.images && Array.isArray(data.images)) {
      if (data.images.length > 5) {
        errors.push("Tối đa 5 ảnh");
      }
    }

    return errors;
  }

  /**
   * Kiểm tra màu hex hợp lệ
   */
  isValidHexColor(color) {
    return /^#[0-9A-F]{6}$/i.test(color);
  }

  /**
   * Format dữ liệu để tương thích với backend
   */
  formatGalaxyData(formData) {
    const data = {
      messages: [],
      icons: [],
      colors: {
        love: "#ff6b9d",
        birthday: "#4ecdc4",
        date: "#c534ed",
        special: "#f34bce",
        heart: "#ff69b4",
      },
      song: "",
    };

    // Parse messages từ textarea
    if (formData.messages) {
      data.messages = formData.messages
        .split("\n")
        .map(msg => msg.trim())
        .filter(msg => msg.length > 0);
    }

    // Parse icons
    if (formData.icons) {
      data.icons = Array.isArray(formData.icons)
        ? formData.icons
        : [formData.icons];
    }

    // Parse colors
    if (formData.colors) {
      Object.assign(data.colors, formData.colors);
    }

    // Song
    if (formData.song) {
      data.song = formData.song;
    }

    return data;
  }

  /**
   * Show loading state
   */
  showLoading(element, text = "Đang xử lý...") {
    if (element) {
      element.disabled = true;
      element.textContent = text;
    }
  }

  /**
   * Hide loading state
   */
  hideLoading(element, originalText = "Gửi") {
    if (element) {
      element.disabled = false;
      element.textContent = originalText;
    }
  }

  /**
   * Show error message
   */
  showError(message, container = null) {
    console.error("Galaxy API Error:", message);

    if (container) {
      const errorDiv = document.createElement("div");
      errorDiv.className = "error-message";
      errorDiv.style.cssText = `
                background: #ff4757;
                color: white;
                padding: 1rem;
                border-radius: 8px;
                margin: 1rem 0;
                text-align: center;
            `;
      errorDiv.textContent = message;

      // Remove existing error messages
      const existingErrors = container.querySelectorAll(".error-message");
      existingErrors.forEach(el => el.remove());

      container.appendChild(errorDiv);

      // Auto remove after 5 seconds
      setTimeout(() => {
        if (errorDiv.parentNode) {
          errorDiv.parentNode.removeChild(errorDiv);
        }
      }, 5000);
    } else {
      alert(message);
    }
  }
}

// Export cho sử dụng global
window.GalaxyAPI = GalaxyAPI;
