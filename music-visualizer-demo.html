<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Music Visualizer Demo - Love Galaxy</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            font-family: 'Arial', sans-serif;
            color: white;
            min-height: 100vh;
        }
        
        .demo-container {
            max-width: 800px;
            margin: 0 auto;
            text-align: center;
        }
        
        h1 {
            margin-bottom: 30px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
        }
        
        .audio-section {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 30px;
            backdrop-filter: blur(10px);
        }
        
        audio {
            width: 100%;
            margin: 20px 0;
        }
        
        .demo-galaxy {
            position: relative;
            height: 400px;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 15px;
            overflow: hidden;
            margin: 20px 0;
        }
        
        .demo-particle {
            position: absolute;
            color: #4ecdc4;
            font-size: 16px;
            font-weight: bold;
            text-shadow: 0 0 10px currentColor;
            animation: float 3s ease-in-out infinite;
        }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }
        
        .controls {
            display: flex;
            gap: 10px;
            justify-content: center;
            flex-wrap: wrap;
            margin: 20px 0;
        }
        
        .btn {
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 8px;
            padding: 10px 20px;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .btn:hover {
            background: rgba(255, 255, 255, 0.3);
        }
        
        .btn.active {
            background: #4ecdc4;
            border-color: #4ecdc4;
        }
        
        .status {
            background: rgba(0, 0, 0, 0.5);
            border-radius: 10px;
            padding: 15px;
            margin: 20px 0;
            font-family: monospace;
            font-size: 14px;
        }
        
        .frequency-bars {
            display: flex;
            justify-content: center;
            align-items: end;
            height: 100px;
            gap: 2px;
            margin: 20px 0;
        }
        
        .frequency-bar {
            width: 8px;
            background: linear-gradient(to top, #4ecdc4, #45b7d1);
            border-radius: 4px 4px 0 0;
            transition: height 0.1s ease;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <h1>🎵 Music Visualizer Demo</h1>
        
        <div class="audio-section">
            <h3>Test Audio</h3>
            <audio id="demoAudio" controls>
                <source src="songs/happy-birthday.mp3" type="audio/mpeg">
                <source src="https://www.soundjay.com/misc/sounds/bell-ringing-05.wav" type="audio/wav">
                Your browser does not support the audio element.
            </audio>
            
            <div class="controls">
                <button class="btn active" id="toggleVisualizer">Enable Visualizer</button>
                <button class="btn" id="sensitivityLow">Low Sensitivity</button>
                <button class="btn active" id="sensitivityMed">Medium Sensitivity</button>
                <button class="btn" id="sensitivityHigh">High Sensitivity</button>
            </div>
        </div>
        
        <div class="demo-galaxy" id="demoGalaxy">
            <div class="demo-particle" style="top: 20%; left: 20%;">❤️ Love</div>
            <div class="demo-particle" style="top: 40%; left: 70%;">💖 You</div>
            <div class="demo-particle" style="top: 60%; left: 30%;">🌟 Forever</div>
            <div class="demo-particle" style="top: 80%; left: 60%;">💫 Always</div>
        </div>
        
        <div class="frequency-bars" id="frequencyBars">
            <!-- Frequency bars will be generated by JavaScript -->
        </div>
        
        <div class="status" id="status">
            <div>Status: <span id="statusText">Ready</span></div>
            <div>Bass: <span id="bassLevel">0</span></div>
            <div>Mid: <span id="midLevel">0</span></div>
            <div>Treble: <span id="trebleLevel">0</span></div>
        </div>
        
        <div style="margin-top: 30px; font-size: 14px; opacity: 0.8;">
            <p>🎧 Play some music and watch the particles dance to the beat!</p>
            <p>💡 Try different sensitivity levels to see the effect</p>
        </div>
    </div>

    <script src="assets/js/music-visualizer.js"></script>
    <script>
        // Demo implementation
        let musicVisualizer = null;
        const audio = document.getElementById('demoAudio');
        const galaxy = document.getElementById('demoGalaxy');
        const statusText = document.getElementById('statusText');
        const bassLevel = document.getElementById('bassLevel');
        const midLevel = document.getElementById('midLevel');
        const trebleLevel = document.getElementById('trebleLevel');
        
        // Create frequency bars
        const frequencyBars = document.getElementById('frequencyBars');
        for (let i = 0; i < 32; i++) {
            const bar = document.createElement('div');
            bar.className = 'frequency-bar';
            bar.style.height = '5px';
            frequencyBars.appendChild(bar);
        }
        
        // Initialize Music Visualizer
        try {
            musicVisualizer = new MusicVisualizer(audio, galaxy);
            
            // Override some methods for demo visualization
            const originalProcessFrequencyData = musicVisualizer.processFrequencyData;
            musicVisualizer.processFrequencyData = function() {
                const result = originalProcessFrequencyData.call(this);
                
                // Update status display
                bassLevel.textContent = (result.bass * 100).toFixed(1) + '%';
                midLevel.textContent = (result.mid * 100).toFixed(1) + '%';
                trebleLevel.textContent = (result.treble * 100).toFixed(1) + '%';
                
                // Update frequency bars
                const bars = frequencyBars.children;
                for (let i = 0; i < bars.length && i < this.dataArray.length; i++) {
                    const height = (this.dataArray[i] / 255) * 100;
                    bars[i].style.height = Math.max(5, height) + 'px';
                }
                
                return result;
            };
            
            statusText.textContent = 'Music Visualizer Ready';
        } catch (error) {
            console.error('Failed to initialize Music Visualizer:', error);
            statusText.textContent = 'Error: ' + error.message;
        }
        
        // Control buttons
        document.getElementById('toggleVisualizer').addEventListener('click', function() {
            if (musicVisualizer) {
                if (this.classList.contains('active')) {
                    musicVisualizer.disable();
                    this.classList.remove('active');
                    this.textContent = 'Enable Visualizer';
                    statusText.textContent = 'Visualizer Disabled';
                } else {
                    musicVisualizer.enable();
                    this.classList.add('active');
                    this.textContent = 'Disable Visualizer';
                    statusText.textContent = 'Visualizer Enabled';
                }
            }
        });
        
        // Sensitivity controls
        const sensitivityBtns = [
            { id: 'sensitivityLow', level: 'low' },
            { id: 'sensitivityMed', level: 'medium' },
            { id: 'sensitivityHigh', level: 'high' }
        ];
        
        sensitivityBtns.forEach(({ id, level }) => {
            document.getElementById(id).addEventListener('click', function() {
                // Remove active class from all sensitivity buttons
                sensitivityBtns.forEach(({ id: btnId }) => {
                    document.getElementById(btnId).classList.remove('active');
                });
                
                // Add active class to clicked button
                this.classList.add('active');
                
                // Update sensitivity
                if (musicVisualizer) {
                    musicVisualizer.setSensitivity(level);
                    statusText.textContent = `Sensitivity: ${level}`;
                }
            });
        });
        
        // Audio event listeners
        audio.addEventListener('play', () => {
            if (musicVisualizer && musicVisualizer.settings.enabled) {
                musicVisualizer.start();
                statusText.textContent = 'Playing with Visualizer';
            } else {
                statusText.textContent = 'Playing (Visualizer Disabled)';
            }
        });
        
        audio.addEventListener('pause', () => {
            if (musicVisualizer) {
                musicVisualizer.stop();
            }
            statusText.textContent = 'Paused';
        });
        
        audio.addEventListener('ended', () => {
            if (musicVisualizer) {
                musicVisualizer.stop();
            }
            statusText.textContent = 'Ended';
        });
        
        // Add some demo particles with base sizes
        const particles = galaxy.querySelectorAll('.demo-particle');
        particles.forEach((particle, index) => {
            particle.dataset.baseSize = '16';
            particle.dataset.baseOpacity = '1';
            
            // Add some random movement
            setInterval(() => {
                if (!musicVisualizer || !musicVisualizer.isActive) {
                    const randomX = Math.random() * 80 + 10; // 10% to 90%
                    const randomY = Math.random() * 80 + 10;
                    particle.style.left = randomX + '%';
                    particle.style.top = randomY + '%';
                }
            }, 3000 + index * 1000);
        });
        
        console.log('Music Visualizer Demo loaded successfully');
    </script>
</body>
</html>
