<?php
// Include config để có thể sử dụng các constants n<PERSON>u cần
require_once __DIR__ . '/config/database.php';
?>
<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no, maximum-scale=1.0, minimum-scale=1.0">
    <title><PERSON><PERSON><PERSON> Hà 3D - Galaxy Generator</title>
    <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        html, body {
            width: 100%;
            height: 100%;
            position: fixed;
            overflow: hidden;
            -webkit-overflow-scrolling: touch;
            overscroll-behavior: none;
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
            background-color: #000; 
        }

        body {
            background: radial-gradient(ellipse at center, #0a0a0a 0%, #000000 100%);
            font-family: 'Orbitron', 'Courier New', monospace;
            perspective: 1500px;
            cursor: grab;
            touch-action: none;
            -webkit-tap-highlight-color: transparent;
            background-color: #000;
        }

        body:active {
            cursor: grabbing;
        }

        .galaxy-container {
            position: absolute;
            top: 50%;
            left: 50%;
            width: 100%;
            height: 100%;
            transform-style: preserve-3d;
            transform: translate(-50%, -50%) rotateX(0deg) rotateY(0deg) scale(1);
            will-change: transform;
        }

        .text-particle {
            position: absolute;
            color: #fff;
            font-size: 14px;
            font-weight: 600;
            white-space: nowrap;
            text-shadow:
                0 0 10px currentColor,
                0 0 20px currentColor,
                0 0 30px currentColor,
                2px 2px 4px rgba(0,0,0,0.8);
            transform-style: preserve-3d;
            pointer-events: none;
            letter-spacing: 1px;
            will-change: transform, opacity;
            backface-visibility: visible;
            transform: translateZ(0);
        }

        /* Mobile optimizations - giảm hiệu ứng */
        @media (max-width: 768px) {
            .text-particle {
                text-shadow:
                    0 0 5px currentColor,
                    0 0 10px currentColor,
                    1px 1px 2px rgba(0,0,0,0.8);
                letter-spacing: 0.5px;
            }
        }

        @media (max-width: 480px) {
            .text-particle {
                text-shadow:
                    0 0 3px currentColor,
                    1px 1px 2px rgba(0,0,0,0.8);
                letter-spacing: 0.3px;
                font-weight: 500;
            }
        }

        .text-particle.love {
            color: #ff6b9d;
            text-shadow: 
                0 0 15px currentColor,
                0 0 25px currentColor,
                0 0 35px currentColor,
                2px 2px 6px rgba(0,0,0,0.9);
        }

        .text-particle.birthday {
            color: #4ecdc4;
            text-shadow: 
                0 0 15px currentColor,
                0 0 25px currentColor,
                0 0 35px currentColor,
                2px 2px 6px rgba(0,0,0,0.9);
        }

        .text-particle.date {
            color: #c534ed;
            text-shadow: 
                0 0 20px currentColor,
                0 0 30px currentColor,
                0 0 40px currentColor,
                2px 2px 6px rgba(0,0,0,0.9);
        }

        .text-particle.special {
            color: #f34bce;
            text-shadow: 
                0 0 15px currentColor,
                0 0 25px currentColor,
                0 0 35px currentColor,
                2px 2px 6px rgba(0,0,0,0.9);
        }

        .text-particle.heart {
            color: #ff69b4;
            text-shadow: 
                0 0 20px currentColor,
                0 0 30px currentColor,
                0 0 40px currentColor,
                3px 3px 8px rgba(0,0,0,0.9);
        }

        .star {
            position: absolute;
            width: 2px;
            height: 2px;
            background: white;
            border-radius: 50%;
            box-shadow: 0 0 10px white;
            animation: twinkle 3s infinite;
            will-change: opacity;
        }

        @keyframes twinkle {
            0%, 100% { opacity: 0.3; }
            50% { opacity: 1; }
        }

        .loading-screen {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            color: white;
            font-family: 'Orbitron', sans-serif;
        }

        .loading-spinner {
            width: 60px;
            height: 60px;
            border: 4px solid rgba(255, 255, 255, 0.3);
            border-top: 4px solid #4ecdc4;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 2rem;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .error-screen {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
            display: none;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            color: white;
            font-family: 'Orbitron', sans-serif;
            text-align: center;
            padding: 2rem;
        }

        .error-icon {
            font-size: 4rem;
            margin-bottom: 1rem;
        }

        .back-home {
            margin-top: 2rem;
            padding: 1rem 2rem;
            background: linear-gradient(45deg, #ff6b9d, #4ecdc4);
            color: white;
            text-decoration: none;
            border-radius: 50px;
            font-weight: 700;
            transition: all 0.3s ease;
        }

        .back-home:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 30px rgba(255, 107, 157, 0.4);
        }

        .image-particle {
            transition: opacity 0.3s;
            will-change: transform, opacity;
            z-index: 2;
        }

        /* Mobile specific styles */
        @media (max-width: 768px) {
            body {
                perspective: 800px; /* Giảm perspective cho mobile */
                transform: translateZ(0); /* Force hardware acceleration */
            }

            .galaxy-container {
                perspective: 800px;
                transform: translateZ(0);
            }

            .audio-control {
                width: 45px;
                height: 45px;
                font-size: 1.3rem;
                top: 15px;
                right: 15px;
                backdrop-filter: none; /* Tắt blur trên mobile */
                background: rgba(255, 255, 255, 0.15);
            }

            .star {
                width: 1.5px;
                height: 1.5px;
                animation: none; /* Tắt animation stars trên mobile */
            }

            /* Tắt một số hiệu ứng nặng */
            .text-particle, .image-particle {
                transform: translateZ(0);
                backface-visibility: visible;
            }
        }

        @media (max-width: 480px) {
            .audio-control {
                width: 40px;
                height: 40px;
                font-size: 1.1rem;
                top: 10px;
                right: 10px;
            }

            .star {
                width: 1px;
                height: 1px;
            }
        }

        /* Landscape orientation on mobile */
        @media (max-height: 500px) and (orientation: landscape) {
            .text-particle {
                font-size: 10px;
            }

            .text-particle.heart {
                font-size: 12px;
            }
        }

        /* Nút điều khiển âm thanh */
        .audio-control {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
            background: rgba(255, 255, 255, 0.1);
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            width: 50px;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            font-size: 1.5rem;
            color: white;
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
            user-select: none;
            -webkit-user-select: none;
            -webkit-tap-highlight-color: transparent;
            touch-action: manipulation;
        }

        .audio-control:hover {
            background: rgba(255, 255, 255, 0.2);
            border-color: rgba(255, 255, 255, 0.5);
            transform: scale(1.1);
        }

        .audio-control:active {
            transform: scale(0.95);
            background: rgba(255, 255, 255, 0.3);
        }

        .audio-control.muted {
            background: rgba(255, 0, 0, 0.2);
            border-color: rgba(255, 0, 0, 0.5);
        }

        /* Music Visualizer Settings Panel */
        .visualizer-settings {
            position: fixed;
            top: 80px;
            right: 20px;
            z-index: 999;
            background: rgba(0, 0, 0, 0.8);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 10px;
            padding: 15px;
            color: white;
            font-family: 'Orbitron', sans-serif;
            font-size: 12px;
            backdrop-filter: blur(10px);
            display: none;
            min-width: 200px;
        }

        .visualizer-settings h4 {
            margin: 0 0 10px 0;
            font-size: 14px;
            color: #4ecdc4;
        }

        .setting-item {
            margin-bottom: 10px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .setting-toggle {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 15px;
            width: 30px;
            height: 16px;
            position: relative;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .setting-toggle.active {
            background: #4ecdc4;
        }

        .setting-toggle::after {
            content: '';
            position: absolute;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: white;
            top: 2px;
            left: 2px;
            transition: all 0.3s ease;
        }

        .setting-toggle.active::after {
            left: 16px;
        }

        .sensitivity-selector {
            display: flex;
            gap: 5px;
        }

        .sensitivity-btn {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 5px;
            padding: 3px 8px;
            color: white;
            cursor: pointer;
            font-size: 10px;
            transition: all 0.3s ease;
        }

        .sensitivity-btn.active {
            background: #4ecdc4;
            border-color: #4ecdc4;
        }

        @media (max-width: 768px) {
            .visualizer-settings {
                top: 70px;
                right: 10px;
                padding: 10px;
                font-size: 11px;
                min-width: 180px;
            }
        }
    </style>
</head>
<body>
    <div class="loading-screen" id="loadingScreen">
        <div class="loading-spinner"></div>
        <h2>Những điều tốt đẹp đều cần thời gian...</h2>
        <p>Vui lòng đợi trong giây lát</p>
    </div>

    <div class="error-screen" id="errorScreen">
        <div class="error-icon">😢</div>
        <h2>Không tìm thấy website</h2>
        <p>Link này có thể đã hết hạn hoặc không tồn tại.</p>
        <a href="index.php" class="back-home">🏠 Về trang chủ</a>
    </div>

    <div class="galaxy-container" id="galaxy">
        <!-- Các text particles sẽ được tạo bằng JavaScript -->
    </div>

    <!-- Nút điều khiển âm thanh -->
    <div class="audio-control" id="audioControl" style="display:none;" title="Bật/Tắt nhạc">
        🔊
    </div>

    <!-- Music Visualizer Settings Panel -->
    <div class="visualizer-settings" id="visualizerSettings">
        <h4>🎵 Music Visualizer</h4>

        <div class="setting-item">
            <span>Enable</span>
            <div class="setting-toggle active" id="toggleVisualizer"></div>
        </div>

        <div class="setting-item">
            <span>Particle Size</span>
            <div class="setting-toggle active" id="toggleParticleSize"></div>
        </div>

        <div class="setting-item">
            <span>Color Effects</span>
            <div class="setting-toggle active" id="toggleColorEffects"></div>
        </div>

        <div class="setting-item">
            <span>Animation Speed</span>
            <div class="setting-toggle active" id="toggleAnimationSpeed"></div>
        </div>

        <div class="setting-item">
            <span>Sensitivity</span>
            <div class="sensitivity-selector">
                <div class="sensitivity-btn" data-level="low">Low</div>
                <div class="sensitivity-btn active" data-level="medium">Med</div>
                <div class="sensitivity-btn" data-level="high">High</div>
            </div>
        </div>
    </div>

    <audio id="galaxyAudio" controls style="display:none"></audio>

    <script src="assets/js/galaxy-api.js"></script>
    <script src="assets/js/music-visualizer.js"></script>
    <script src="assets/js/galaxy-viewer.js"></script>
</body>
</html>
