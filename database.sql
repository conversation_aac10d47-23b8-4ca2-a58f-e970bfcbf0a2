-- Tạo database cho Galaxy Generator
CREATE DATABASE IF NOT EXISTS galaxy_generator CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE galaxy_generator;

-- Bảng galaxies - l<PERSON><PERSON> thông tin chính của galaxy
CREATE TABLE galaxies (
    id VARCHAR(50) PRIMARY KEY,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    song VARCHAR(500) NULL,
    love_color VARCHAR(7) DEFAULT '#ff6b9d',
    birthday_color VARCHAR(7) DEFAULT '#4ecdc4',
    date_color VARCHAR(7) DEFAULT '#c534ed',
    special_color VARCHAR(7) DEFAULT '#f34bce',
    heart_color VARCHAR(7) DEFAULT '#ff69b4',
    performance_level ENUM('low', 'medium', 'high') DEFAULT 'medium'
);

-- Bảng galaxy_messages - l<PERSON><PERSON> các tin nhắn của galaxy
CREATE TABLE galaxy_messages (
    id INT AUTO_INCREMENT PRIMARY KEY,
    galaxy_id VARCHAR(50) NOT NULL,
    message TEXT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (galaxy_id) REFERENCES galaxies(id) ON DELETE CASCADE,
    INDEX idx_galaxy_id (galaxy_id)
);

-- Bảng galaxy_icons - lưu các biểu tượng của galaxy
CREATE TABLE galaxy_icons (
    id INT AUTO_INCREMENT PRIMARY KEY,
    galaxy_id VARCHAR(50) NOT NULL,
    icon VARCHAR(10) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (galaxy_id) REFERENCES galaxies(id) ON DELETE CASCADE,
    INDEX idx_galaxy_id (galaxy_id)
);

-- Bảng galaxy_images - lưu đường dẫn ảnh của galaxy
CREATE TABLE galaxy_images (
    id INT AUTO_INCREMENT PRIMARY KEY,
    galaxy_id VARCHAR(50) NOT NULL,
    image_url VARCHAR(1000) NOT NULL,
    original_name VARCHAR(255) NULL,
    file_size INT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (galaxy_id) REFERENCES galaxies(id) ON DELETE CASCADE,
    INDEX idx_galaxy_id (galaxy_id)
);

-- Tạo user cho ứng dụng (tùy chọn)
-- CREATE USER 'galaxy_user'@'localhost' IDENTIFIED BY 'your_password_here';
-- GRANT SELECT, INSERT, UPDATE, DELETE ON galaxy_generator.* TO 'galaxy_user'@'localhost';
-- FLUSH PRIVILEGES;

-- Thêm dữ liệu demo
INSERT INTO galaxies (id, song, love_color, birthday_color, date_color, special_color, heart_color) 
VALUES ('demo', 'eyenoselip.mp3', '#ff6b9d', '#4ecdc4', '#ff69b4', '#ff6b9d', '#ff69b4');

INSERT INTO galaxy_messages (galaxy_id, message) VALUES 
('demo', 'I love you so much! ❤️'),
('demo', 'Our Anniverasry'),
('demo', 'I love you 💖'),
('demo', '25/08/2004'),
('demo', 'Thank you for being my sunshine '),
('demo', 'Thank you for being my everything 💕'),
('demo', 'You are my universe '),
('demo', 'There is no other'),
('demo', 'You''re amazing'),
('demo', 'You make my heart smile '),
('demo', 'Love ya! 💖'),
('demo', 'Honey bunch, you are my everything! ');

INSERT INTO galaxy_icons (galaxy_id, icon) VALUES 
('demo', '♥'),
('demo', '💖'),
('demo', '❤️'),
('demo', '❤️'),
('demo', '💕'),
('demo', '💕');

INSERT INTO galaxy_images (galaxy_id, image_url) VALUES 
('demo', 'https://firebasestorage.googleapis.com/v0/b/staynowapp1.appspot.com/o/galaxy-images%2Fz6654939498839_d275d5b163f80fd572ba1403f32746fa.jpg?alt=media&token=759411f6-2c26-43ac-b1c0-bf9e31752744');
