/**
 * Music Visualization Engine for Love Galaxy
 * Synchronizes visual effects with audio frequency data
 */

class MusicVisualizer {
  constructor(audioElement, galaxyContainer) {
    this.audio = audioElement;
    this.container = galaxyContainer;
    this.isInitialized = false;
    this.isActive = false;
    this.animationId = null;
    
    // Audio analysis setup
    this.audioContext = null;
    this.analyser = null;
    this.source = null;
    this.dataArray = null;
    this.bufferLength = 0;
    
    // Visualization settings
    this.settings = {
      enabled: true,
      sensitivity: 'medium', // low, medium, high
      effects: {
        particleSize: true,
        colorIntensity: true,
        animationSpeed: true,
        cameraEffects: false // Disabled for mobile by default
      }
    };
    
    // Effect parameters
    this.baseParticleSize = 1;
    this.baseAnimationSpeed = 1;
    this.baseColorIntensity = 1;
    
    // Smoothing for visual effects
    this.smoothedBass = 0;
    this.smoothedMid = 0;
    this.smoothedTreble = 0;
    this.smoothingFactor = 0.8;
    
    this.init();
  }
  
  async init() {
    try {
      // Create audio context
      this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
      
      // Setup analyser
      this.analyser = this.audioContext.createAnalyser();
      this.analyser.fftSize = 256;
      this.analyser.smoothingTimeConstant = 0.8;
      this.bufferLength = this.analyser.frequencyBinCount;
      this.dataArray = new Uint8Array(this.bufferLength);
      
      // Connect audio source
      if (!this.source) {
        this.source = this.audioContext.createMediaElementSource(this.audio);
        this.source.connect(this.analyser);
        this.analyser.connect(this.audioContext.destination);
      }
      
      this.isInitialized = true;
      console.log('Music Visualizer initialized successfully');
      
    } catch (error) {
      console.error('Failed to initialize Music Visualizer:', error);
      this.settings.enabled = false;
    }
  }
  
  start() {
    if (!this.isInitialized || !this.settings.enabled) {
      return;
    }
    
    // Resume audio context if suspended
    if (this.audioContext.state === 'suspended') {
      this.audioContext.resume();
    }
    
    this.isActive = true;
    this.animate();
    console.log('Music Visualizer started');
  }
  
  stop() {
    this.isActive = false;
    if (this.animationId) {
      cancelAnimationFrame(this.animationId);
      this.animationId = null;
    }
    console.log('Music Visualizer stopped');
  }
  
  animate() {
    if (!this.isActive) return;
    
    // Get frequency data
    this.analyser.getByteFrequencyData(this.dataArray);
    
    // Process frequency bands
    const frequencyData = this.processFrequencyData();
    
    // Apply visual effects
    this.applyVisualEffects(frequencyData);
    
    // Continue animation
    this.animationId = requestAnimationFrame(() => this.animate());
  }
  
  processFrequencyData() {
    // Divide frequency spectrum into bands
    const bassEnd = Math.floor(this.bufferLength * 0.1); // 0-10%
    const midEnd = Math.floor(this.bufferLength * 0.4);  // 10-40%
    const trebleEnd = this.bufferLength;                 // 40-100%
    
    // Calculate average for each band
    let bassSum = 0, midSum = 0, trebleSum = 0;
    
    for (let i = 0; i < bassEnd; i++) {
      bassSum += this.dataArray[i];
    }
    
    for (let i = bassEnd; i < midEnd; i++) {
      midSum += this.dataArray[i];
    }
    
    for (let i = midEnd; i < trebleEnd; i++) {
      trebleSum += this.dataArray[i];
    }
    
    // Normalize values (0-1)
    const bass = (bassSum / bassEnd) / 255;
    const mid = (midSum / (midEnd - bassEnd)) / 255;
    const treble = (trebleSum / (trebleEnd - midEnd)) / 255;
    
    // Apply smoothing
    this.smoothedBass = this.smoothedBass * this.smoothingFactor + bass * (1 - this.smoothingFactor);
    this.smoothedMid = this.smoothedMid * this.smoothingFactor + mid * (1 - this.smoothingFactor);
    this.smoothedTreble = this.smoothedTreble * this.smoothingFactor + treble * (1 - this.smoothingFactor);
    
    return {
      bass: this.smoothedBass,
      mid: this.smoothedMid,
      treble: this.smoothedTreble,
      overall: (this.smoothedBass + this.smoothedMid + this.smoothedTreble) / 3
    };
  }
  
  applyVisualEffects(frequencyData) {
    // Apply sensitivity multiplier
    const sensitivity = this.getSensitivityMultiplier();
    
    if (this.settings.effects.particleSize) {
      this.adjustParticleSize(frequencyData.bass * sensitivity);
    }
    
    if (this.settings.effects.animationSpeed) {
      this.adjustAnimationSpeed(frequencyData.mid * sensitivity);
    }
    
    if (this.settings.effects.colorIntensity) {
      this.adjustColorIntensity(frequencyData.treble * sensitivity);
    }
    
    if (this.settings.effects.cameraEffects) {
      this.adjustCameraEffects(frequencyData.overall * sensitivity);
    }
  }
  
  getSensitivityMultiplier() {
    switch (this.settings.sensitivity) {
      case 'low': return 0.5;
      case 'medium': return 1.0;
      case 'high': return 2.0;
      default: return 1.0;
    }
  }
  
  adjustParticleSize(bassLevel) {
    const sizeMultiplier = 1 + (bassLevel * 2); // 1x to 3x size
    const particles = this.container.querySelectorAll('.text-particle, .image-particle');
    
    particles.forEach(particle => {
      const baseSize = parseFloat(particle.dataset.baseSize || '14');
      particle.style.fontSize = `${baseSize * sizeMultiplier}px`;
      
      // Store base size for future reference
      if (!particle.dataset.baseSize) {
        particle.dataset.baseSize = baseSize.toString();
      }
    });
  }
  
  adjustAnimationSpeed(midLevel) {
    const speedMultiplier = 0.5 + (midLevel * 1.5); // 0.5x to 2x speed
    const container = this.container;
    
    // Adjust CSS animation duration
    const currentDuration = parseFloat(container.style.animationDuration || '20s');
    const newDuration = (20 / speedMultiplier);
    
    container.style.animationDuration = `${newDuration}s`;
  }
  
  adjustColorIntensity(trebleLevel) {
    const intensityMultiplier = 0.7 + (trebleLevel * 0.6); // 0.7x to 1.3x intensity
    const particles = this.container.querySelectorAll('.text-particle');
    
    particles.forEach(particle => {
      const baseOpacity = parseFloat(particle.dataset.baseOpacity || '0.9');
      particle.style.opacity = Math.min(1, baseOpacity * intensityMultiplier);
      
      // Store base opacity for future reference
      if (!particle.dataset.baseOpacity) {
        particle.dataset.baseOpacity = baseOpacity.toString();
      }
      
      // Adjust text shadow intensity
      const shadowIntensity = Math.floor(10 + (trebleLevel * 20));
      particle.style.textShadow = `
        0 0 ${shadowIntensity}px currentColor,
        0 0 ${shadowIntensity * 2}px currentColor,
        0 0 ${shadowIntensity * 3}px currentColor,
        2px 2px 4px rgba(0,0,0,0.8)
      `;
    });
  }
  
  adjustCameraEffects(overallLevel) {
    // Subtle camera shake/zoom based on overall audio level
    const shakeIntensity = overallLevel * 2;
    const zoomLevel = 1 + (overallLevel * 0.05); // 1x to 1.05x zoom
    
    const shakeX = (Math.random() - 0.5) * shakeIntensity;
    const shakeY = (Math.random() - 0.5) * shakeIntensity;
    
    this.container.style.transform = `
      scale(${zoomLevel}) 
      translateX(${shakeX}px) 
      translateY(${shakeY}px)
    `;
  }
  
  // Settings management
  updateSettings(newSettings) {
    this.settings = { ...this.settings, ...newSettings };
    
    // Disable camera effects on mobile for performance
    if (this.isMobile()) {
      this.settings.effects.cameraEffects = false;
    }
  }
  
  isMobile() {
    return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
  }
  
  // Public API
  enable() {
    this.settings.enabled = true;
    if (this.isInitialized && !this.isActive) {
      this.start();
    }
  }
  
  disable() {
    this.settings.enabled = false;
    this.stop();
  }
  
  setSensitivity(level) {
    if (['low', 'medium', 'high'].includes(level)) {
      this.settings.sensitivity = level;
    }
  }
  
  toggleEffect(effectName, enabled) {
    if (this.settings.effects.hasOwnProperty(effectName)) {
      this.settings.effects[effectName] = enabled;
    }
  }
}

// Export for global use
window.MusicVisualizer = MusicVisualizer;
