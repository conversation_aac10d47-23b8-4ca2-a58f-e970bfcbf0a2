/**
 * Music Visualization Engine for Love Galaxy
 * Synchronizes visual effects with audio frequency data
 */

class MusicVisualizer {
  constructor(audioElement, galaxyContainer) {
    this.audio = audioElement;
    this.container = galaxyContainer;
    this.isInitialized = false;
    this.isActive = false;
    this.animationId = null;

    // Audio analysis setup
    this.audioContext = null;
    this.analyser = null;
    this.source = null;
    this.dataArray = null;
    this.bufferLength = 0;

    // Performance optimization - cache DOM elements
    this.particles = [];
    this.lastUpdate = 0;
    this.updateInterval = 50; // Update every 50ms instead of every frame

    // Visualization settings - simplified for performance
    this.settings = {
      enabled: true,
      sensitivity: "low", // Default to low for better performance
      effects: {
        particleSize: true,
        colorIntensity: false, // Disabled by default for performance
        animationSpeed: false, // Disabled by default for performance
        cameraEffects: false, // Always disabled for performance
      },
    };

    // Smoothing for visual effects
    this.smoothedBass = 0;
    this.smoothedMid = 0;
    this.smoothedTreble = 0;
    this.smoothingFactor = 0.85; // More smoothing for less jarky movement

    this.init();
  }

  async init() {
    try {
      // Create audio context
      this.audioContext = new (window.AudioContext ||
        window.webkitAudioContext)();

      // Setup analyser
      this.analyser = this.audioContext.createAnalyser();
      this.analyser.fftSize = 256;
      this.analyser.smoothingTimeConstant = 0.8;
      this.bufferLength = this.analyser.frequencyBinCount;
      this.dataArray = new Uint8Array(this.bufferLength);

      // Connect audio source
      if (!this.source) {
        this.source = this.audioContext.createMediaElementSource(this.audio);
        this.source.connect(this.analyser);
        this.analyser.connect(this.audioContext.destination);
      }

      // Cache DOM elements for performance
      this.cacheParticles();

      this.isInitialized = true;
      console.log("Music Visualizer initialized successfully");
    } catch (error) {
      console.error("Failed to initialize Music Visualizer:", error);
      this.settings.enabled = false;
    }
  }

  cacheParticles() {
    // Cache particles to avoid DOM queries in animation loop
    this.particles = Array.from(
      this.container.querySelectorAll(".text-particle, .image-particle")
    );

    // Store base values for each particle
    this.particles.forEach(particle => {
      if (!particle.dataset.baseSize) {
        const computedStyle = window.getComputedStyle(particle);
        particle.dataset.baseSize = parseFloat(computedStyle.fontSize) || 14;
      }
      if (!particle.dataset.baseOpacity) {
        particle.dataset.baseOpacity = particle.style.opacity || "0.9";
      }
    });
  }

  start() {
    if (!this.isInitialized || !this.settings.enabled) {
      return;
    }

    // Resume audio context if suspended
    if (this.audioContext.state === "suspended") {
      this.audioContext.resume();
    }

    this.isActive = true;
    this.animate();
    console.log("Music Visualizer started");
  }

  stop() {
    this.isActive = false;
    if (this.animationId) {
      cancelAnimationFrame(this.animationId);
      this.animationId = null;
    }
    console.log("Music Visualizer stopped");
  }

  animate() {
    if (!this.isActive) return;

    const now = performance.now();

    // Throttle updates for better performance
    if (now - this.lastUpdate >= this.updateInterval) {
      // Get frequency data
      this.analyser.getByteFrequencyData(this.dataArray);

      // Process frequency bands
      const frequencyData = this.processFrequencyData();

      // Apply visual effects (only enabled ones)
      this.applyVisualEffects(frequencyData);

      this.lastUpdate = now;
    }

    // Continue animation
    this.animationId = requestAnimationFrame(() => this.animate());
  }

  processFrequencyData() {
    // Divide frequency spectrum into bands
    const bassEnd = Math.floor(this.bufferLength * 0.1); // 0-10%
    const midEnd = Math.floor(this.bufferLength * 0.4); // 10-40%
    const trebleEnd = this.bufferLength; // 40-100%

    // Calculate average for each band
    let bassSum = 0,
      midSum = 0,
      trebleSum = 0;

    for (let i = 0; i < bassEnd; i++) {
      bassSum += this.dataArray[i];
    }

    for (let i = bassEnd; i < midEnd; i++) {
      midSum += this.dataArray[i];
    }

    for (let i = midEnd; i < trebleEnd; i++) {
      trebleSum += this.dataArray[i];
    }

    // Normalize values (0-1)
    const bass = bassSum / bassEnd / 255;
    const mid = midSum / (midEnd - bassEnd) / 255;
    const treble = trebleSum / (trebleEnd - midEnd) / 255;

    // Apply smoothing
    this.smoothedBass =
      this.smoothedBass * this.smoothingFactor +
      bass * (1 - this.smoothingFactor);
    this.smoothedMid =
      this.smoothedMid * this.smoothingFactor +
      mid * (1 - this.smoothingFactor);
    this.smoothedTreble =
      this.smoothedTreble * this.smoothingFactor +
      treble * (1 - this.smoothingFactor);

    return {
      bass: this.smoothedBass,
      mid: this.smoothedMid,
      treble: this.smoothedTreble,
      overall: (this.smoothedBass + this.smoothedMid + this.smoothedTreble) / 3,
    };
  }

  applyVisualEffects(frequencyData) {
    // Apply sensitivity multiplier
    const sensitivity = this.getSensitivityMultiplier();

    if (this.settings.effects.particleSize) {
      this.adjustParticleSize(frequencyData.bass * sensitivity);
    }

    if (this.settings.effects.animationSpeed) {
      this.adjustAnimationSpeed(frequencyData.mid * sensitivity);
    }

    if (this.settings.effects.colorIntensity) {
      this.adjustColorIntensity(frequencyData.treble * sensitivity);
    }

    if (this.settings.effects.cameraEffects) {
      this.adjustCameraEffects(frequencyData.overall * sensitivity);
    }
  }

  getSensitivityMultiplier() {
    switch (this.settings.sensitivity) {
      case "low":
        return 0.5;
      case "medium":
        return 1.0;
      case "high":
        return 2.0;
      default:
        return 1.0;
    }
  }

  adjustParticleSize(bassLevel) {
    if (!this.settings.effects.particleSize) return;

    const sizeMultiplier = 1 + bassLevel * 0.5; // Reduced from 2 to 0.5 for subtler effect

    // Use cached particles instead of DOM query
    this.particles.forEach(particle => {
      const baseSize = parseFloat(particle.dataset.baseSize);
      particle.style.fontSize = `${baseSize * sizeMultiplier}px`;
    });
  }

  adjustAnimationSpeed(midLevel) {
    // Disabled for performance - animation duration changes cause reflow
    return;
  }

  adjustColorIntensity(trebleLevel) {
    // Disabled for performance - text shadow and opacity changes are expensive
    return;
  }

  adjustCameraEffects(overallLevel) {
    // Always disabled for performance - transform changes cause reflow
    return;
  }

  // Settings management
  updateSettings(newSettings) {
    this.settings = { ...this.settings, ...newSettings };

    // Disable camera effects on mobile for performance
    if (this.isMobile()) {
      this.settings.effects.cameraEffects = false;
    }
  }

  isMobile() {
    return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
      navigator.userAgent
    );
  }

  // Public API
  enable() {
    this.settings.enabled = true;
    if (this.isInitialized && !this.isActive) {
      this.start();
    }
  }

  disable() {
    this.settings.enabled = false;
    this.stop();
  }

  setSensitivity(level) {
    if (["low", "medium", "high"].includes(level)) {
      this.settings.sensitivity = level;
    }
  }

  toggleEffect(effectName, enabled) {
    if (this.settings.effects.hasOwnProperty(effectName)) {
      this.settings.effects[effectName] = enabled;
    }
  }
}

// Export for global use
window.MusicVisualizer = MusicVisualizer;
