<?php
/**
 * API endpoint để upload bài hát
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: ' . CORS_ORIGIN);
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight request
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Chỉ cho phép POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'error' => 'Method not allowed']);
    exit();
}

try {
    // Include các file cần thiết
    require_once __DIR__ . '/../config/database.php';
    
    // Kiểm tra có file upload không
    if (empty($_FILES['song'])) {
        throw new Exception('No song uploaded');
    }
    
    // Tạo thư mục songs nếu chưa có
    $songsDir = __DIR__ . '/../songs/';
    if (!is_dir($songsDir)) {
        if (!mkdir($songsDir, 0755, true)) {
            throw new Exception('Cannot create songs directory');
        }
    }
    
    $file = $_FILES['song'];
    
    // Validate file
    if ($file['error'] !== UPLOAD_ERR_OK) {
        throw new Exception('Upload error: ' . $file['error']);
    }
    
    // Validate file size (max 10MB)
    $maxSize = 10 * 1024 * 1024; // 10MB
    if ($file['size'] > $maxSize) {
        throw new Exception('File size too large. Max 10MB allowed.');
    }
    
    // Validate file extension
    $allowedExtensions = ['mp3', 'ogg', 'wav', 'm4a', 'aac'];
    $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
    if (!in_array($extension, $allowedExtensions)) {
        throw new Exception('Invalid file type. Allowed: ' . implode(', ', $allowedExtensions));
    }
    
    // Validate MIME type
    $allowedMimes = [
        'audio/mpeg',
        'audio/mp3',
        'audio/ogg',
        'audio/wav',
        'audio/x-wav',
        'audio/mp4',
        'audio/aac'
    ];
    
    $finfo = finfo_open(FILEINFO_MIME_TYPE);
    $mimeType = finfo_file($finfo, $file['tmp_name']);
    finfo_close($finfo);
    
    if (!in_array($mimeType, $allowedMimes)) {
        throw new Exception('Invalid audio file format');
    }
    
    // Tạo tên file unique
    $filename = uniqid() . '_' . time() . '.' . $extension;
    $filepath = $songsDir . $filename;
    
    // Move file
    if (!move_uploaded_file($file['tmp_name'], $filepath)) {
        throw new Exception('Failed to save uploaded file');
    }
    
    // Trả về tên file (không phải full path)
    http_response_code(200);
    echo json_encode([
        'success' => true,
        'filename' => $filename,
        'original_name' => $file['name']
    ]);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
?>
